# Deferred Registration Implementation with Anonymous Authentication

## Table of Contents
1. [Architecture Overview](#architecture-overview)
2. [User Journey Workflows](#user-journey-workflows)
3. [Technical Implementation Details](#technical-implementation-details)
4. [Component Interaction Diagrams](#component-interaction-diagrams)
5. [API Flow Diagrams](#api-flow-diagrams)
6. [Developer Guide](#developer-guide)

## Architecture Overview

The deferred registration system integrates anonymous authentication with the existing Supabase infrastructure to eliminate upfront registration barriers while maintaining data security and user account functionality.

### High-Level System Architecture

```mermaid
graph TB
    subgraph "Frontend Application"
        HP[HomePage]
        QZ[Certificate Quiz]
        DE[Data Entry Pages]
        SM[Summary Page]
        PM[Payment Modal]
        AC[Account Conversion]
    end
    
    subgraph "Authentication Layer"
        AUTH[AuthContext]
        ANON[Anonymous Auth]
        PERM[Permanent Auth]
        CONV[Account Conversion]
    end
    
    subgraph "Supabase Backend"
        SA[Supabase Auth]
        DB[(Database)]
        ST[Storage]
        RLS[Row Level Security]
    end
    
    subgraph "External Services"
        STRIPE[Stripe Payment]
        EMAIL[Email Service]
    end
    
    HP --> QZ
    QZ --> ANON
    ANON --> SA
    SA --> DE
    DE --> DB
    DE --> ST
    DE --> SM
    SM --> AC
    AC --> CONV
    CONV --> PERM
    SM --> PM
    PM --> STRIPE
    STRIPE --> EMAIL
    
    RLS --> DB
    AUTH --> SA
```

### Key Components Integration

graph TB
    subgraph "User Interface Layer"
        HP[HomePage<br/>Certificate Quiz]
        OBJ[ObjektdatenPage<br/>Email Capture]
        FORMS[Data Entry Forms<br/>GebaeudedetailsPage<br/>FensterPage<br/>HeizungPage<br/>etc.]
        SUM[ZusammenfassungPage<br/>Account Conversion]
        PAY[Payment Flow<br/>Stripe Integration]
    end
    
    subgraph "Authentication Layer"
        AC[AuthContext<br/>- isAnonymous<br/>- signInAnonymously<br/>- linkIdentity]
        UAA[useAnonymousAuth Hook<br/>- ensureAuthenticated<br/>- createCertificateWithAuth]
        ACM[AccountConversionModal<br/>Password Creation]
        AUI[AnonymousUserIndicator<br/>User Status Display]
    end
    
    subgraph "Business Logic Layer"
        CC[CertificateContext<br/>Certificate Management]
        RT[Route Protection<br/>checkAuthOrAnonymous]
        AU[Account Conversion Utils<br/>Email Extraction<br/>Conflict Resolution]
    end
    
    subgraph "Supabase Backend"
        SA[Supabase Auth<br/>Anonymous Users<br/>Account Linking]
        DB[(Database<br/>energieausweise<br/>RLS Policies)]
        ST[Storage<br/>File Uploads<br/>Directory Structure]
    end
    
    subgraph "External Services"
        STRIPE[Stripe<br/>Payment Processing]
        EMAIL[Email Service<br/>Notifications]
    end
    
    %% User Flow
    HP --> UAA
    UAA --> AC
    AC --> SA
    SA --> OBJ
    OBJ --> AUI
    OBJ --> FORMS
    FORMS --> CC
    CC --> DB
    FORMS --> ST
    FORMS --> SUM
    SUM --> ACM
    ACM --> AU
    AU --> AC
    SUM --> PAY
    PAY --> STRIPE
    STRIPE --> EMAIL
    
    %% Route Protection
    RT --> AC
    RT --> SA
    
    %% Data Flow
    CC --> DB
    DB --> CC
    
    %% Styling
    classDef userInterface fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef auth fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef business fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef backend fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef external fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    
    class HP,OBJ,FORMS,SUM,PAY userInterface
    class AC,UAA,ACM,AUI auth
    class CC,RT,AU business
    class SA,DB,ST backend
    class STRIPE,EMAIL external

- **Anonymous Authentication**: Supabase anonymous users for immediate access
- **Certificate Management**: Seamless certificate creation without registration
- **Account Conversion**: Optional upgrade to permanent account at payment stage
- **Data Persistence**: Maintains certificate ownership through user transitions
- **Security**: RLS policies ensure data isolation for anonymous users

## User Journey Workflows

flowchart TD
    A[User visits Homepage] --> B[Takes Certificate Type Quiz]
    B --> C{Quiz Result}
    C -->|WG/V| D[Verbrauchsausweis]
    C -->|WG/B| E[Bedarfsausweis]
    C -->|NWG/V| F[Nichtwohngebäude]
    
    D --> G[Click 'Create Certificate']
    E --> G
    F --> G
    
    G --> H{User Authenticated?}
    H -->|No| I[Auto Sign-in Anonymously]
    H -->|Yes Anonymous| J[Continue with Anonymous]
    H -->|Yes Permanent| K[Continue with Permanent Account]
    
    I --> L[Show Success Message]
    L --> M[Navigate to ObjektdatenPage]
    J --> M
    K --> M
    
    M --> N[Show Anonymous User Indicator]
    N --> O[Capture Email Address - Required]
    O --> P[Continue Data Entry Flow]
    
    P --> Q[GebaeudedetailsPage1]
    Q --> R[GebaeudedetailsPage2]
    R --> S{Certificate Type}
    
    S -->|WG/B| T[FensterPage]
    S -->|WG/V, NWG/V| U[Skip to Next]
    
    T --> V[HeizungPage - WG/B only]
    V --> W[TwwLueftungPage - WG/B only]
    W --> X[Skip VerbrauchPage]
    
    U --> Y[VerbrauchPage - WG/V, NWG/V only]
    Y --> Z[ZusammenfassungPage]
    X --> Z
    
    Z --> AA[Show Data Summary]
    AA --> BB[Show Anonymous Indicator]
    BB --> CC[Legal Consent Checkboxes]
    CC --> DD{All Consents Given?}
    
    DD -->|No| EE[Show Error Message]
    EE --> CC
    
    DD -->|Yes| FF[Click Payment Button]
    FF --> GG{Is Anonymous User?}
    
    GG -->|No| HH[Proceed to Stripe Checkout]
    GG -->|Yes| II[Show Account Conversion Modal]
    
    II --> JJ{User Choice}
    JJ -->|Create Account| KK[Enter Password]
    JJ -->|Skip - Stay Anonymous| LL[Proceed as Anonymous]
    
    KK --> MM[Validate Password]
    MM -->|Invalid| NN[Show Error]
    NN --> KK
    
    MM -->|Valid| OO[Call linkIdentity API]
    OO -->|Success| PP[Convert to Permanent User]
    OO -->|Error| QQ[Show Conversion Error]
    QQ --> JJ
    
    PP --> RR[Update Auth State]
    RR --> SS[Close Modal]
    LL --> SS
    SS --> HH
    
    HH --> TT[Stripe Payment Process]
    TT --> UU[Payment Success]
    UU --> VV[Send Email Notification]
    VV --> WW[Show Success Page]
    
    %% Styling
    classDef startEnd fill:#e8f5e8,stroke:#4caf50,stroke-width:3px
    classDef process fill:#e3f2fd,stroke:#2196f3,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#ff9800,stroke-width:2px
    classDef anonymous fill:#f3e5f5,stroke:#9c27b0,stroke-width:2px
    classDef payment fill:#ffebee,stroke:#f44336,stroke-width:2px
    
    class A,WW startEnd
    class G,I,L,M,N,O,P,Q,R,T,V,W,Y,Z,AA,BB,KK,MM,OO,PP,RR,SS,TT,UU,VV process
    class C,H,S,DD,GG,JJ decision
    class I,J,L,N,II,LL,PP anonymous
    class FF,HH,TT,UU payment

### 1. Anonymous User Flow - Certificate Creation

```mermaid
flowchart TD
    A[User visits Homepage] --> B[Takes Certificate Quiz]
    B --> C[Selects Certificate Type]
    C --> D{User Authenticated?}
    D -->|No| E[Auto Sign-in Anonymously]
    D -->|Yes| F[Check if Anonymous]
    E --> G[Create Certificate]
    F -->|Anonymous| G
    F -->|Permanent| G
    G --> H[Navigate to Data Entry]
    H --> I[Show Anonymous Indicator]
    I --> J[Capture Email on First Page]
    J --> K[Continue Data Entry Flow]
    K --> L[Complete All Forms]
    L --> M[Navigate to Summary]
    
    style E fill:#e1f5fe
    style I fill:#e8f5e8
    style J fill:#fff3e0
```

### 2. Account Conversion Process

```mermaid
flowchart TD
    A[User at Summary Page] --> B{Is Anonymous User?}
    B -->|No| C[Proceed to Payment]
    B -->|Yes| D[Click Payment Button]
    D --> E[Show Account Conversion Modal]
    E --> F{User Choice}
    F -->|Create Account| G[Enter Password]
    F -->|Skip| H[Proceed as Anonymous]
    G --> I[Validate Password]
    I -->|Valid| J[Call linkIdentity API]
    I -->|Invalid| K[Show Error]
    K --> G
    J -->|Success| L[Update Auth State]
    J -->|Error| M[Show Conversion Error]
    L --> N[Close Modal]
    H --> N
    N --> C
    M --> F
    
    style G fill:#e8f5e8
    style J fill:#e1f5fe
    style L fill:#f3e5f5
```

### 3. Data Persistence During Account Conversion

```mermaid
sequenceDiagram
    participant U as User
    participant AC as AuthContext
    participant SB as Supabase Auth
    participant DB as Database
    participant ST as Storage
    
    U->>AC: Request Account Conversion
    AC->>SB: updateUser(email, password)
    SB->>SB: Convert Anonymous → Permanent
    SB-->>AC: Return Updated User
    
    Note over SB,DB: User ID remains same
    Note over DB: Certificate ownership preserved
    Note over ST: File access maintained
    
    AC->>U: Conversion Success
    U->>U: Proceed to Payment
```

### 4. Error Handling and Edge Cases

```mermaid
flowchart TD
    A[Anonymous User Session] --> B{Session Valid?}
    B -->|Yes| C[Continue Normal Flow]
    B -->|No| D[Session Expired]
    D --> E[Auto Re-authenticate Anonymously]
    E -->|Success| F[Restore Session]
    E -->|Failure| G[Redirect to Homepage]

    H[Account Conversion] --> I{Email Exists?}
    I -->|No| J[Create Account Successfully]
    I -->|Yes| K[Show Email Conflict Error]
    K --> L[Suggest Login Instead]

    M[File Upload] --> N{Anonymous User?}
    N -->|Yes| O[Check Session Validity]
    N -->|No| P[Normal Upload Flow]
    O -->|Valid| Q[Upload to Anonymous Path]
    O -->|Invalid| R[Re-authenticate & Retry]

    style D fill:#ffebee
    style K fill:#ffebee
    style R fill:#ffebee
```

## Technical Implementation Details

### Authentication State Management

The authentication system now supports three user states:

1. **Unauthenticated**: No session, redirected to login for protected routes
2. **Anonymous**: Temporary session, can access certificate creation
3. **Permanent**: Full account with email/password, access to all features

```typescript
interface AuthContextType {
  session: Session | null;
  user: User | null;
  loading: boolean;
  isAnonymous: boolean;  // New property
  signInAnonymously: () => Promise<AuthResponse>;  // New method
  linkIdentity: (email: string, password: string) => Promise<AuthResponse>;  // New method
}
```

### Route Protection Logic

```mermaid
flowchart TD
    A[Route Access Request] --> B{Route Type}
    B -->|Public| C[Allow Access]
    B -->|Certificate Data Entry| D[checkAuthOrAnonymous]
    B -->|Admin/Account| E[checkAuth]

    D --> F{User Session?}
    F -->|Yes| G[Allow Access]
    F -->|No| H[Redirect to Login]

    E --> I{Permanent User?}
    I -->|Yes| J[Allow Access]
    I -->|No| K[Redirect to Login]

    style G fill:#e8f5e8
    style J fill:#e8f5e8
    style H fill:#ffebee
    style K fill:#ffebee
```

### Database Schema Implications

The existing database schema requires no changes as anonymous users receive regular Supabase user IDs. Key considerations:

- **User ID Persistence**: Anonymous user IDs remain constant during account conversion
- **RLS Policies**: Existing policies work seamlessly with anonymous users
- **Certificate Ownership**: `user_id` foreign key maintains data relationships
- **File Storage**: Directory structure based on user ID remains consistent

### File Storage Handling

```mermaid
 graph LR
     subgraph "Storage Structure"
         A[certificateuploads/]
         A --> B["userId/"]
         B --> C["certificateId/"]
         C --> D[fieldName_filename.ext]
     end
 
     subgraph "Access Control"
         E[RLS Policy]
         E --> F["user_id = auth.uid()"]
         F --> G[Works for Anonymous & Permanent]
     end
 

    style B fill:#e1f5fe
    style F fill:#e8f5e8
```

## Component Interaction Diagrams

### 1. New Components Integration

```mermaid
graph TB
    subgraph "Existing Components"
        AC[AuthContext]
        CC[CertificateContext]
        HP[HomePage]
        OP[ObjektdatenPage]
        ZP[ZusammenfassungPage]
    end

    subgraph "New Components"
        UAA[useAnonymousAuth Hook]
        AUI[AnonymousUserIndicator]
        ACM[AccountConversionModal]
        AU[accountConversion Utils]
    end

    HP --> UAA
    UAA --> AC
    UAA --> CC

    OP --> AUI
    AUI --> AC

    ZP --> ACM
    ACM --> AU
    AU --> AC

    style UAA fill:#e8f5e8
    style AUI fill:#fff3e0
    style ACM fill:#f3e5f5
    style AU fill:#e1f5fe
```

### 2. Authentication Flow Integration

```mermaid
sequenceDiagram
    participant HP as HomePage
    participant UAA as useAnonymousAuth
    participant AC as AuthContext
    participant SB as Supabase
    participant CC as CertificateContext

    HP->>UAA: createCertificateWithAuth(type)
    UAA->>AC: ensureAuthenticated()
    AC->>SB: signInAnonymously()
    SB-->>AC: Anonymous Session
    AC-->>UAA: Authentication Success
    UAA->>CC: createNewCertificate(type)
    CC->>SB: Insert Certificate
    SB-->>CC: Certificate ID
    CC-->>UAA: Success
    UAA-->>HP: Certificate Created
```

### 3. Account Conversion Component Flow

```mermaid
sequenceDiagram
    participant ZP as ZusammenfassungPage
    participant ACM as AccountConversionModal
    participant AU as accountConversion Utils
    participant AC as AuthContext
    participant SB as Supabase

    ZP->>ZP: handleCheckout() - Anonymous User
    ZP->>ACM: Show Modal
    ACM->>ACM: User Enters Password
    ACM->>AU: handleAccountConversion(password)
    AU->>AU: getUserEmailFromCertificate()
    AU->>AC: linkIdentity(email, password)
    AC->>SB: updateUser({email, password})
    SB-->>AC: Updated User
    AC-->>AU: Success
    AU-->>ACM: Conversion Complete
    ACM->>ZP: onSuccess()
    ZP->>ZP: Proceed to Payment
```

## API Flow Diagrams

### 1. Anonymous Sign-in Process

```mermaid
sequenceDiagram
    participant C as Client
    participant AC as AuthContext
    participant SB as Supabase Auth
    participant DB as Database

    C->>AC: signInAnonymously()
    AC->>SB: auth.signInAnonymously()
    SB->>SB: Create Anonymous User
    SB->>DB: Insert User Record
    DB-->>SB: User ID
    SB-->>AC: {user, session}
    AC->>AC: setIsAnonymous(true)
    AC-->>C: Authentication Success

    Note over SB,DB: User gets permanent UUID
    Note over AC: isAnonymous = user.is_anonymous
```

### 2. Certificate Creation with Anonymous User

```mermaid
sequenceDiagram
    participant C as Client
    participant CC as CertificateContext
    participant SB as Supabase
    participant DB as Database

    C->>CC: createNewCertificate(type)
    CC->>SB: auth.getUser()
    SB-->>CC: Anonymous User
    CC->>DB: INSERT energieausweise
    Note over DB: user_id = anonymous_user_id
    DB-->>CC: Certificate Record
    CC->>CC: Generate Order Number
    CC->>DB: UPDATE order_number
    DB-->>CC: Updated Certificate
    CC-->>C: Certificate ID
```

### 3. Account Linking/Conversion Process

```mermaid
sequenceDiagram
    participant C as Client
    participant AC as AuthContext
    participant SB as Supabase Auth
    participant DB as Database
    participant ST as Storage

    C->>AC: linkIdentity(email, password)
    AC->>SB: auth.updateUser({email, password})
    SB->>SB: Convert Anonymous → Permanent
    Note over SB: Same user_id maintained
    SB->>DB: UPDATE auth.users SET is_anonymous=false
    SB-->>AC: Updated User Object
    AC->>AC: setIsAnonymous(false)
    AC-->>C: Conversion Success

    Note over DB: All certificates remain linked
    Note over ST: File access preserved
    Note over SB: Email confirmation may be required
```

### 4. Data Migration During Conversion

```mermaid
flowchart LR
    subgraph "Before Conversion"
        A1[Anonymous User]
        A2[user_id: uuid-123]
        A3[is_anonymous: true]
        A4[email: null]
    end

    subgraph "Conversion Process"
        B1[updateUser API Call]
        B2[Same UUID Maintained]
        B3[Add Email & Password]
    end

    subgraph "After Conversion"
        C1[Permanent User]
        C2[user_id: uuid-123]
        C3[is_anonymous: false]
        C4[email: <EMAIL>]
    end

    subgraph "Data Relationships"
        D1[Certificates]
        D2[File Uploads]
        D3[Order History]
    end

    A1 --> B1
    B1 --> C1
    A2 --> B2
    B2 --> C2

    C2 --> D1
    C2 --> D2
    C2 --> D3

    style B2 fill:#e8f5e8
    style C2 fill:#e8f5e8
```

## Developer Guide

### Configuration Requirements

#### 1. Supabase Authentication Settings

Enable anonymous authentication in your Supabase project:

```bash
# Via Supabase Dashboard
Authentication > Settings > Anonymous sign-ins: Enabled

# Via API (already configured)
PATCH /v1/projects/{project_id}/config/auth
{
  "external_anonymous_users_enabled": true,
  "security_manual_linking_enabled": true
}
```

#### 2. Environment Variables

No additional environment variables required. Uses existing Supabase configuration.

### Code Examples

#### 1. Using the Anonymous Authentication Hook

```typescript
import { useAnonymousAuth } from '../hooks/useAnonymousAuth';

const MyComponent = () => {
  const { createCertificateWithAuth, isAnonymous } = useAnonymousAuth();

  const handleCreateCertificate = async (type: string) => {
    const certificateId = await createCertificateWithAuth(type);
    if (certificateId) {
      // Navigate to data entry
      navigate('/erfassen/objektdaten');
    }
  };

  return (
    <div>
      {isAnonymous && <AnonymousUserIndicator />}
      <button onClick={() => handleCreateCertificate('WG/V')}>
        Create Certificate
      </button>
    </div>
  );
};
```

#### 2. Implementing Account Conversion

```typescript
import { AccountConversionModal } from '../components/auth/AccountConversionModal';
import { getUserEmailFromCertificate } from '../utils/accountConversion';

const PaymentPage = () => {
  const { isAnonymous } = useAuth();
  const [showConversion, setShowConversion] = useState(false);
  const [userEmail, setUserEmail] = useState<string | null>(null);

  useEffect(() => {
    if (isAnonymous && activeCertificateId) {
      getUserEmailFromCertificate(activeCertificateId)
        .then(setUserEmail);
    }
  }, [isAnonymous, activeCertificateId]);

  const handlePayment = () => {
    if (isAnonymous) {
      setShowConversion(true);
    } else {
      proceedToPayment();
    }
  };

  return (
    <>
      <button onClick={handlePayment}>Pay Now</button>
      {isAnonymous && userEmail && (
        <AccountConversionModal
          isOpen={showConversion}
          onClose={() => setShowConversion(false)}
          onSuccess={() => {
            setShowConversion(false);
            proceedToPayment();
          }}
          userEmail={userEmail}
        />
      )}
    </>
  );
};
```

#### 3. Route Protection Implementation

```typescript
// routeLoaders.ts
export async function checkAuthOrAnonymous(redirectTo: string) {
  const { data: { session } } = await supabase.auth.getSession();

  if (!session) {
    throw redirect({
      to: '/login',
      search: { redirect: redirectTo },
    });
  }

  return { session, user: session.user };
}

// Route definition
const dataEntryRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/erfassen/objektdaten',
  component: ObjektdatenPage,
  loader: async () => {
    await checkAuthOrAnonymous('/erfassen/objektdaten');
  },
});
```

### Integration Points

#### 1. Authentication Context Integration

The `AuthContext` has been extended to support anonymous authentication:

```typescript
// Key additions to AuthContext
interface AuthContextType {
  // ... existing properties
  isAnonymous: boolean;
  signInAnonymously: () => Promise<AuthResponse>;
  linkIdentity: (email: string, password: string) => Promise<AuthResponse>;
}
```

#### 2. Certificate Context Integration

No changes required to `CertificateContext`. It automatically works with anonymous users since they receive regular Supabase user IDs.

#### 3. Payment Flow Integration

The payment flow now includes an optional account conversion step:

```mermaid
flowchart TD
    A[User clicks Pay] --> B{Is Anonymous?}
    B -->|No| C[Proceed to Stripe]
    B -->|Yes| D[Show Conversion Modal]
    D --> E{User Choice}
    E -->|Convert| F[Create Account]
    E -->|Skip| G[Continue Anonymous]
    F --> H[Update Auth State]
    G --> C
    H --> C
    C --> I[Stripe Checkout]
```

### Maintenance Considerations

#### 1. Session Management

Anonymous sessions have the same timeout behavior as regular sessions. Consider implementing:

- **Session refresh logic** for long data entry sessions
- **Auto-save functionality** to prevent data loss
- **Session expiration warnings** for anonymous users

#### 2. Data Cleanup

Implement cleanup strategies for abandoned anonymous sessions:

```sql
-- Example cleanup query (run periodically)
DELETE FROM energieausweise
WHERE user_id IN (
  SELECT id FROM auth.users
  WHERE is_anonymous = true
  AND created_at < NOW() - INTERVAL '7 days'
  AND last_sign_in_at < NOW() - INTERVAL '24 hours'
);
```

#### 3. Analytics and Monitoring

Track key metrics for the anonymous authentication system:

- **Conversion rate**: Anonymous users who create permanent accounts
- **Abandonment rate**: Anonymous sessions that don't complete
- **Session duration**: Time spent in anonymous state
- **Error rates**: Account conversion failures

#### 4. Security Considerations

- **Rate limiting**: Prevent abuse of anonymous sign-in endpoint
- **Data validation**: Ensure email addresses are valid before conversion
- **Conflict resolution**: Handle cases where anonymous user email matches existing account

### Testing Strategy

#### 1. Unit Tests

```typescript
// Example test for anonymous authentication
describe('useAnonymousAuth', () => {
  it('should create certificate with automatic authentication', async () => {
    const { result } = renderHook(() => useAnonymousAuth());

    const certificateId = await result.current.createCertificateWithAuth('WG/V');

    expect(certificateId).toBeDefined();
    expect(mockSignInAnonymously).toHaveBeenCalled();
    expect(mockCreateNewCertificate).toHaveBeenCalledWith('WG/V');
  });
});
```

#### 2. Integration Tests

- Test complete anonymous user flow from homepage to payment
- Test account conversion process with various scenarios
- Test data persistence during account conversion
- Test error handling for session expiration and conflicts

#### 3. End-to-End Tests

```typescript
// Example E2E test
test('Anonymous user can complete certificate creation', async ({ page }) => {
  await page.goto('/');
  await page.click('[data-testid="certificate-quiz-start"]');
  // ... complete quiz
  await page.click('[data-testid="create-certificate"]');

  // Should be automatically signed in anonymously
  await expect(page.locator('[data-testid="anonymous-indicator"]')).toBeVisible();

  // Complete data entry
  await page.fill('[name="Kunden_email"]', '<EMAIL>');
  // ... fill other forms

  // Should reach payment with conversion option
  await expect(page.locator('[data-testid="account-conversion-modal"]')).toBeVisible();
});
```

### Troubleshooting Guide

#### Common Issues

1. **Anonymous sign-in fails**
   - Check Supabase project settings
   - Verify `external_anonymous_users_enabled: true`
   - Check network connectivity

2. **Account conversion fails**
   - Verify email format validation
   - Check for existing account conflicts
   - Ensure `security_manual_linking_enabled: true`

3. **Data not persisting after conversion**
   - Verify user ID remains constant during conversion
   - Check RLS policies are correctly configured
   - Ensure certificate ownership is maintained

4. **File uploads fail for anonymous users**
   - Check storage RLS policies
   - Verify bucket permissions
   - Ensure directory structure is correct

This comprehensive implementation provides a seamless user experience while maintaining security and data integrity throughout the anonymous-to-permanent user conversion process.
