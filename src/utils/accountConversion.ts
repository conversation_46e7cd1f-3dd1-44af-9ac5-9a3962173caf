import { supabase } from '../lib/supabase';

/**
 * Utility functions for converting anonymous users to permanent accounts
 */

/**
 * Gets the user's email from their certificate data
 */
export const getUserEmailFromCertificate = async (certificateId: string): Promise<string | null> => {
  try {
    const { data, error } = await supabase
      .from('energieausweise')
      .select('objektdaten')
      .eq('id', certificateId)
      .single();

    if (error || !data?.objektdaten) {
      return null;
    }

    const objektdaten = data.objektdaten as any;
    return objektdaten?.Kunden_email || null;
  } catch (error) {
    console.error('Error getting email from certificate:', error);
    return null;
  }
};

/**
 * Converts an anonymous user to a permanent user with email and password
 */
export const convertAnonymousUser = async (email: string, password: string): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    const { error } = await supabase.auth.updateUser({
      email,
      password,
    });

    if (error) {
      return {
        success: false,
        error: error.message,
      };
    }

    return { success: true };
  } catch (error: any) {
    return {
      success: false,
      error: error.message || 'Unbekannter <PERSON>hler beim <PERSON>ieren des Kontos',
    };
  }
};

/**
 * Checks if the current user is anonymous
 */
export const isCurrentUserAnonymous = async (): Promise<boolean> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    return user?.is_anonymous ?? false;
  } catch (error) {
    console.error('Error checking if user is anonymous:', error);
    return false;
  }
};

/**
 * Gets the current user's active certificate ID
 */
export const getActiveCertificateId = (): string | null => {
  return localStorage.getItem('activeCertificateId');
};

/**
 * Handles the complete account conversion flow
 */
export const handleAccountConversion = async (password: string): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    // Get the active certificate ID
    const certificateId = getActiveCertificateId();
    if (!certificateId) {
      return {
        success: false,
        error: 'Kein aktives Zertifikat gefunden',
      };
    }

    // Get the user's email from the certificate
    const email = await getUserEmailFromCertificate(certificateId);
    if (!email) {
      return {
        success: false,
        error: 'E-Mail-Adresse nicht in den Zertifikatsdaten gefunden',
      };
    }

    // Convert the anonymous user
    const result = await convertAnonymousUser(email, password);
    return result;
  } catch (error: any) {
    return {
      success: false,
      error: error.message || 'Fehler beim Konvertieren des Kontos',
    };
  }
};

/**
 * Checks if an email already exists in the system (for conflict detection)
 */
export const checkEmailExists = async (email: string): Promise<boolean> => {
  try {
    // Try to sign in with a dummy password to check if email exists
    // This is a workaround since Supabase doesn't provide a direct way to check email existence
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password: 'dummy-password-for-check',
    });

    // If we get an "Invalid login credentials" error, the email exists but password is wrong
    // If we get "Email not confirmed" error, the email exists
    // If we get other errors, we assume email doesn't exist
    if (error) {
      return error.message.includes('Invalid login credentials') || 
             error.message.includes('Email not confirmed');
    }

    return true; // If no error, email exists and password was correct (unlikely with dummy password)
  } catch (error) {
    console.error('Error checking email existence:', error);
    return false;
  }
};
